import React from 'react';
import { View } from 'react-native';
import { Text } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import { useTranslation } from 'react-i18next';

interface ProfileStatsProps {
  posts: number;
  followers: number;
  following: number;
}

export function ProfileStats({
  posts,
  followers,
  following,
}: ProfileStatsProps) {
  const theme = usePaperTheme();
  const { t } = useTranslation();

  const StatItem = ({ value, label }: { value: number; label: string }) => (
    <View
      style={{
        alignItems: 'center',
        flex: 1,
        paddingVertical: theme.spacing?.sm || 8,
      }}
    >
      <Text
        variant="headlineMedium"
        style={{
          color: theme.colors.onSurfaceVariant,
          fontWeight: '700',
          marginBottom: theme.spacing?.xs || 4,
          letterSpacing: 0.25,
        }}
      >
        {value.toLocaleString()}
      </Text>
      <Text
        variant="labelLarge"
        style={{
          color: theme.colors.onSurfaceVariant,
          fontWeight: '500',
          textTransform: 'uppercase',
          letterSpacing: 0.5,
        }}
      >
        {label}
      </Text>
    </View>
  );

  return (
    <View
      style={{
        flexDirection: 'row',
        justifyContent: 'space-around',
        paddingVertical: theme.spacing?.xl || 32,
        paddingHorizontal: theme.spacing?.lg || 24,
        marginHorizontal: theme.spacing?.lg || 24,
        backgroundColor: theme.colors.surfaceVariant,
        borderRadius: theme.roundness || 12,
        elevation: 1,
      }}
    >
      <StatItem value={posts} label={t('profile.stories', '故事')} />
      <StatItem value={followers} label={t('profile.followers', '粉丝')} />
      <StatItem value={following} label={t('profile.following', '关注')} />
    </View>
  );
}
