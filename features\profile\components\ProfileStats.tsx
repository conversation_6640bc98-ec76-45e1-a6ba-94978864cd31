import React from 'react';
import { View } from 'react-native';
import { Text } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import { useTranslation } from 'react-i18next';

interface ProfileStatsProps {
  posts: number;
  followers: number;
  following: number;
}

export function ProfileStats({
  posts,
  followers,
  following,
}: ProfileStatsProps) {
  const theme = usePaperTheme();
  const { t } = useTranslation();

  const StatItem = ({ value, label }: { value: number; label: string }) => (
    <View
      style={{
        alignItems: 'center',
        flex: 1,
      }}
    >
      <Text
        variant="headlineSmall"
        style={{
          color: theme.colors.onSurface,
          fontWeight: 'bold',
          marginBottom: theme.spacing?.xs || 4,
        }}
      >
        {value}
      </Text>
      <Text
        variant="bodyMedium"
        style={{
          color: theme.colors.onSurfaceVariant,
        }}
      >
        {label}
      </Text>
    </View>
  );

  return (
    <View
      style={{
        flexDirection: 'row',
        justifyContent: 'space-around',
        paddingVertical: theme.spacing?.lg || 24,
        paddingHorizontal: theme.spacing?.md || 16,
        marginHorizontal: theme.spacing?.md || 16,
        backgroundColor: theme.colors.surface,
        borderRadius: theme.roundness || 12,
        elevation: 1,
      }}
    >
      <StatItem value={posts} label={t('profile.stories', '故事')} />
      <StatItem value={followers} label={t('profile.followers', '粉丝')} />
      <StatItem value={following} label={t('profile.following', '关注')} />
    </View>
  );
}
