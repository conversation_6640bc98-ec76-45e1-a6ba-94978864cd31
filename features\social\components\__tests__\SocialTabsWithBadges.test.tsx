import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { PaperProvider } from 'react-native-paper';
import { SocialTabsWithBadges } from '../SocialTabsWithBadges';

// Mock the API functions
jest.mock('@/api/notifications', () => ({
  getUnreadNotificationCount: jest.fn(() => Promise.resolve({ count: 3 })),
}));

jest.mock('@/api/messages', () => ({
  getUnreadMessageCount: jest.fn(() => Promise.resolve({ count: 5 })),
}));

// Mock react-i18next
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string, defaultValue?: string) => defaultValue || key,
  }),
}));

const renderWithProvider = (component: React.ReactElement) => {
  return render(<PaperProvider>{component}</PaperProvider>);
};

describe('SocialTabsWithBadges', () => {
  const mockOnTabPress = jest.fn();

  beforeEach(() => {
    mockOnTabPress.mockClear();
    require('@/api/notifications')
      .getUnreadNotificationCount.mockClear()
      .mockResolvedValue({ count: 3 });
    require('@/api/messages')
      .getUnreadMessageCount.mockClear()
      .mockResolvedValue({ count: 5 });
  });

  it('renders all tabs correctly and fetches counts', async () => {
    const { getByText, findByText } = renderWithProvider(
      <SocialTabsWithBadges activeTab="feed" onTabPress={mockOnTabPress} />
    );

    await waitFor(() => {
      expect(getByText('动态')).toBeTruthy();
      expect(getByText('发现')).toBeTruthy();
      expect(getByText(/消息/)).toBeTruthy();
      expect(getByText(/通知/)).toBeTruthy();
    });

    expect(
      require('@/api/notifications').getUnreadNotificationCount
    ).toHaveBeenCalled();
    expect(require('@/api/messages').getUnreadMessageCount).toHaveBeenCalled();
  });

  it('calls onTabPress when a tab is pressed', async () => {
    const { getByText } = renderWithProvider(
      <SocialTabsWithBadges activeTab="feed" onTabPress={mockOnTabPress} />
    );

    await waitFor(() => expect(getByText('发现')).toBeTruthy());

    fireEvent.press(getByText('发现'));
    expect(mockOnTabPress).toHaveBeenCalledWith('discover');
  });

  it('shows the correct active tab', async () => {
    const { getByText } = renderWithProvider(
      <SocialTabsWithBadges activeTab="messages" onTabPress={mockOnTabPress} />
    );

    await waitFor(() => {
      expect(getByText(/消息/)).toBeTruthy();
    });
  });
});
