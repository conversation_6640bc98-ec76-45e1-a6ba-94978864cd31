import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { PaperProvider } from 'react-native-paper';
import { SocialTabsWithBadges } from '../SocialTabsWithBadges';

// Mock the API functions
jest.mock('@/api/notifications', () => ({
  getUnreadNotificationCount: jest.fn(() => Promise.resolve({ count: 3 })),
}));

jest.mock('@/api/messages', () => ({
  getUnreadMessageCount: jest.fn(() => Promise.resolve({ count: 5 })),
}));

// Mock react-i18next
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string, defaultValue: string) => defaultValue,
  }),
}));

const renderWithProvider = (component: React.ReactElement) => {
  return render(<PaperProvider>{component}</PaperProvider>);
};

describe('SocialTabsWithBadges', () => {
  const mockOnTabPress = jest.fn();

  beforeEach(() => {
    mockOnTabPress.mockClear();
  });

  it('renders all tabs correctly', () => {
    const { getByText } = renderWithProvider(
      <SocialTabsWithBadges activeTab="feed" onTabPress={mockOnTabPress} />
    );

    expect(getByText('动态')).toBeTruthy();
    expect(getByText('发现')).toBeTruthy();
    expect(getByText('消息')).toBeTruthy();
    expect(getByText('通知')).toBeTruthy();
  });

  it('calls onTabPress when a tab is pressed', () => {
    const { getByText } = renderWithProvider(
      <SocialTabsWithBadges activeTab="feed" onTabPress={mockOnTabPress} />
    );

    fireEvent.press(getByText('发现'));
    expect(mockOnTabPress).toHaveBeenCalledWith('discover');
  });

  it('shows the correct active tab', () => {
    const { getByText } = renderWithProvider(
      <SocialTabsWithBadges activeTab="messages" onTabPress={mockOnTabPress} />
    );

    // The active tab should be visually different (this would need more specific testing based on styling)
    expect(getByText('消息')).toBeTruthy();
  });
});
