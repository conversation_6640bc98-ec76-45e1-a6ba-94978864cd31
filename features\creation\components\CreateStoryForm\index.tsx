import React from 'react';
import { View } from 'react-native';
import { TextInput, Button, Text, HelperText } from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { usePaperTheme } from '@/hooks/usePaperTheme';

interface CreateStoryFormProps {
  title: string;
  onTitleChange: (text: string) => void;
  initialContent: string;
  onContentChange: (text: string) => void;
  contentFocused: boolean;
  onContentFocus: () => void;
  onContentBlur: () => void;
  isFormValid: boolean;
  submitting: boolean;
  onSubmit: () => void;
}

export default function CreateStoryForm({
  title,
  onTitleChange,
  initialContent,
  onContentChange,
  contentFocused,
  onContentFocus,
  onContentBlur,
  isFormValid,
  submitting,
  onSubmit,
}: CreateStoryFormProps) {
  const { t } = useTranslation();
  const theme = usePaperTheme();

  return (
    <>
      {/* Title Input Section */}
      <View style={{ marginBottom: 24 }}>
        <TextInput
          mode="outlined"
          label={t('storyForm.titleLabel', '标题')}
          value={title}
          onChangeText={onTitleChange}
          placeholder={t('storyForm.titlePlaceholder', '输入故事标题')}
          left={<TextInput.Icon icon="bookmark-outline" />}
          maxLength={100}
          style={{
            marginBottom: 4,
            backgroundColor: theme.colors.surface,
          }}
          contentStyle={{
            fontSize: 16,
            lineHeight: 24,
          }}
          outlineStyle={{
            borderRadius: 12, // MD3 推荐的圆角
          }}
        />
        {title.length > 0 && (
          <HelperText type="info" visible={true} style={{ marginLeft: 16 }}>
            {title.length}/100
          </HelperText>
        )}
      </View>

      {/* Content Input Section */}
      <View style={{ marginBottom: 24 }}>
        <TextInput
          mode="outlined"
          label={t('storyForm.initialContentLabel', '开始你的故事')}
          value={initialContent}
          onChangeText={onContentChange}
          placeholder={t('storyForm.initialContentPlaceholder', '从前...')}
          left={<TextInput.Icon icon="book-open-page-variant-outline" />}
          multiline
          numberOfLines={8}
          onFocus={onContentFocus}
          onBlur={onContentBlur}
          style={{
            marginBottom: 4,
            backgroundColor: theme.colors.surface,
            minHeight: 120, // MD3 推荐的多行输入最小高度
          }}
          contentStyle={{
            fontSize: 16,
            lineHeight: 24,
            paddingTop: 16, // 多行输入的顶部内边距
          }}
          outlineStyle={{
            borderRadius: 12, // MD3 推荐的圆角
          }}
        />
        {initialContent.length > 0 && (
          <HelperText
            type={initialContent.length < 50 ? 'error' : 'info'}
            visible={true}
            style={{ marginLeft: 16 }}
          >
            {initialContent.length < 50
              ? `${t('storyForm.contentMinLength', '至少需要50个字符')} (${
                  initialContent.length
                }/50)`
              : `${initialContent.length} ${t(
                  'storyForm.characters',
                  '个字符'
                )}`}
          </HelperText>
        )}
      </View>

      {/* Helper Text Section */}
      <HelperText
        type="info"
        visible={true}
        style={{
          marginBottom: 32,
          marginLeft: 16,
          color: theme.colors.onSurfaceVariant,
        }}
      >
        {t('storyForm.tip', '提示：一个好的开头能够吸引读者继续阅读你的故事。')}
      </HelperText>

      {/* Submit Button Section */}
      <Button
        mode="contained"
        onPress={onSubmit}
        disabled={submitting || !isFormValid}
        loading={submitting}
        style={{
          paddingVertical: 8,
          borderRadius: 24, // MD3 推荐的按钮圆角
          elevation: 2,
        }}
        labelStyle={{
          fontSize: 16,
          fontWeight: '600',
          paddingVertical: 4,
        }}
        testID="submit-story-button"
      >
        {t('storyForm.submitButton', '创建故事')}
      </Button>
    </>
  );
}
