import React, { useState, useCallback } from 'react';
import { FlatList, RefreshControl } from 'react-native';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import ActivityFeedItem from '@/components/social/ActivityFeedItem';
import { useTranslation } from 'react-i18next';
import { generateRandomActivities } from '@/utils/mockData';
import { ActivityItem } from '@/utils/mockData/activities';
import { EmptyState } from './FeedTab/EmptyState';
import { ListHeader } from './FeedTab/ListHeader';
import { ListFooter } from './FeedTab/ListFooter';

interface FeedTabProps {
  activityItems: ActivityItem[];
}

export function FeedTab({ activityItems: initialItems }: FeedTabProps) {
  const theme = usePaperTheme();
  const { t } = useTranslation();

  // 状态管理
  const [refreshing, setRefreshing] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [activityItems, setActivityItems] = useState(initialItems);

  // 模拟下拉刷新操作
  const onRefresh = useCallback(async () => {
    setRefreshing(true);

    // 模拟网络请求延迟
    setTimeout(() => {
      // 生成新的随机活动条目
      const newItems = generateRandomActivities(5);
      // 使用新生成的条目替换当前条目
      setActivityItems(newItems);
      setRefreshing(false);
    }, 1500);
  }, []);

  // 模拟加载更多操作
  const onLoadMore = useCallback(async () => {
    if (loadingMore) return;

    setLoadingMore(true);

    // 模拟网络请求延迟
    setTimeout(() => {
      // 生成额外的随机活动条目
      const newItems = generateRandomActivities(3);
      // 将新条目添加到当前列表末尾
      setActivityItems((prevItems) => [...prevItems, ...newItems]);
      setLoadingMore(false);
    }, 1000);
  }, [loadingMore]);

  // 处理导航到发现页面
  const handleDiscoverPress = () => {
    // 导航到发现页面的逻辑
  };

  return (
    <FlatList
      data={activityItems}
      renderItem={({ item }) => <ActivityFeedItem key={item.id} item={item} />}
      keyExtractor={(item) => item.id}
      contentContainerStyle={{
        flexGrow: 1,
        backgroundColor: theme.colors.background,
      }}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={onRefresh}
          colors={[theme.colors.primary]}
          tintColor={theme.colors.primary}
          title={t('social.feed.refreshing', '刷新中...')}
          titleColor={theme.colors.onSurfaceVariant}
        />
      }
      ListHeaderComponent={<ListHeader />}
      ListEmptyComponent={<EmptyState onDiscoverPress={handleDiscoverPress} />}
      ListFooterComponent={<ListFooter isLoading={loadingMore} />}
      onEndReached={onLoadMore}
      onEndReachedThreshold={0.3}
      showsVerticalScrollIndicator={false}
      style={{
        flex: 1,
        backgroundColor: theme.colors.background,
      }}
    />
  );
}
