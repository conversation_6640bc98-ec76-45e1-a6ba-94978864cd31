import React from 'react';
import { Card, Text, Icon } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import { StoryTheme } from '@/types/story';

interface ThemeSelectionCardProps {
  theme: StoryTheme;
  isSelected: boolean;
  onSelect: () => void;
}

export default function ThemeSelectionCard({
  theme: themeProp,
  isSelected,
  onSelect,
}: ThemeSelectionCardProps) {
  const theme = usePaperTheme();

  const getIconSource = (): string => {
    switch (themeProp.icon) {
      case 'rocket':
        return 'rocket-launch';
      case 'wand':
        return 'magic-staff';
      case 'search':
        return 'magnify';
      case 'heart':
        return 'heart';
      case 'landmark':
        return 'bank';
      case 'building':
        return 'office-building';
      default:
        return 'book-open-page-variant';
    }
  };

  const cardBackgroundColor = isSelected
    ? themeProp.color || theme.colors.primaryContainer
    : theme.colors.surface;
  const textColor = isSelected
    ? theme.colors.onPrimary
    : theme.colors.onSurface;
  const descriptionColor = isSelected
    ? theme.colors.onPrimary
    : theme.colors.onSurfaceVariant;
  const iconColor = isSelected
    ? theme.colors.onPrimary
    : themeProp.color || theme.colors.primary;

  return (
    <Card
      mode={isSelected ? 'contained' : 'outlined'}
      onPress={onSelect}
      style={{
        width: '48%',
        marginBottom: theme.spacing?.md || 16,
        backgroundColor: cardBackgroundColor,
        borderColor: themeProp.color || theme.colors.outline,
        borderWidth: isSelected ? 2 : 1,
      }}
    >
      <Card.Content
        style={{
          padding: theme.spacing?.md || 16,
          alignItems: 'center',
          minHeight: 120,
          justifyContent: 'space-between',
        }}
      >
        <Text
          variant="titleMedium"
          style={{
            color: textColor,
            textAlign: 'center',
            marginBottom: theme.spacing?.sm || 8,
          }}
        >
          {themeProp.name}
        </Text>

        <Icon source={getIconSource()} size={32} color={iconColor} />

        <Text
          variant="bodySmall"
          style={{
            color: descriptionColor,
            textAlign: 'center',
            marginTop: theme.spacing?.sm || 8,
          }}
          numberOfLines={2}
        >
          {themeProp.description}
        </Text>

        {isSelected && (
          <Icon
            source="check"
            size={16}
            color={theme.colors.onPrimary}
            style={{
              position: 'absolute',
              top: theme.spacing?.xs || 4,
              right: theme.spacing?.xs || 4,
            }}
          />
        )}
      </Card.Content>
    </Card>
  );
}
