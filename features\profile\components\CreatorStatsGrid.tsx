import React from 'react';
import { View } from 'react-native';
import { Text, Icon, Card } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import { useTranslation } from 'react-i18next';

interface CreatorStats {
  totalStories: number;
  contributionStreak: number;
  totalLikes: number;
  avgCompletionRate: number;
}

interface CreatorStatsGridProps {
  stats: CreatorStats;
}

interface StatCardProps {
  iconSource: string;
  iconColor: string;
  value: string | number;
  label: string;
}

// Internal component for each stat card
const StatCard = ({ iconSource, iconColor, value, label }: StatCardProps) => {
  const theme = usePaperTheme();
  return (
    <Card
      mode="outlined"
      style={{
        flex: 1,
        margin: theme.spacing?.xs || 4,
        padding: theme.spacing?.md || 16,
        alignItems: 'center',
      }}
    >
      <View style={{ marginBottom: theme.spacing?.sm || 8 }}>
        <Icon source={iconSource} size={24} color={iconColor} />
      </View>
      <Text
        variant="headlineSmall"
        style={{
          color: theme.colors.onSurface,
          marginBottom: theme.spacing?.xs || 4,
        }}
      >
        {value}
      </Text>
      <Text
        variant="bodySmall"
        style={{
          color: theme.colors.onSurfaceVariant,
          textAlign: 'center',
        }}
      >
        {label}
      </Text>
    </Card>
  );
};

export function CreatorStatsGrid({ stats }: CreatorStatsGridProps) {
  const theme = usePaperTheme();
  const { t } = useTranslation();

  return (
    <View style={{ marginBottom: theme.spacing?.lg || 24 }}>
      <Text
        variant="titleLarge"
        style={{
          color: theme.colors.onSurface,
          marginBottom: theme.spacing?.md || 16,
        }}
      >
        {t('creatorStats')}
      </Text>
      <View
        style={{
          flexDirection: 'row',
          flexWrap: 'wrap',
          marginHorizontal: -(theme.spacing?.xs || 4),
        }}
      >
        <StatCard
          iconSource="book-open-variant"
          iconColor={theme.colors.primary}
          value={stats.totalStories}
          label={t('totalStories')}
        />
        <StatCard
          iconSource="trophy"
          iconColor={theme.colors.secondary}
          value={stats.contributionStreak}
          label={t('contributionStreak')}
        />
        <StatCard
          iconSource="heart"
          iconColor={theme.colors.error}
          value={stats.totalLikes}
          label={t('totalLikes')}
        />
        <StatCard
          iconSource="star"
          iconColor={theme.colors.tertiary}
          value={`${stats.avgCompletionRate}%`}
          label={t('avgCompletionRate')}
        />
      </View>
    </View>
  );
}
