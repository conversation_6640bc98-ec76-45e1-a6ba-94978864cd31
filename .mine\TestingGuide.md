# SupaPose 项目测试指南

本文档旨在提供 SupaPose 项目中单元测试和组件测试的简明指南，包括技术栈、关键配置、测试运行方法及常见问题处理。

## 1. 测试技术栈

我们的项目主要使用以下技术进行测试：

- **Jest**: JavaScript 测试运行器，我们使用 `jest-expo`预设，它为 Expo 项目提供了合理的默认配置。
- **React Native Testing Library (`@testing-library/react-native`)**: 提供用于测试 React Native 组件的实用工具，鼓励编写用户中心化的测试。
- **TypeScript**: 所有测试代码均使用 TypeScript 编写，以提高代码质量和可维护性。

## 2. 关键配置文件和配置点

正确的 Jest 配置对于顺利运行测试至关重要。

### a. `jest.config.js`

这是 Jest 的主要配置文件。关键配置项包括：

- `preset: 'jest-expo'`: 使用 `jest-expo` 的预设配置。
- `transformIgnorePatterns: []`: **非常重要**。将其设置为空数组 `[]` 会强制 Jest 转换所有 `node_modules` 中的包。这对于许多 React Native 和 Expo 的依赖包（它们可能使用 ES6+ 语法或 JSX）是必需的，否则可能导致 `SyntaxError`。
- `setupFilesAfterEnv: ['./jest-setup.js']`: 指定一个或多个在每个测试文件执行前运行的模块，用于设置测试环境。

### b. `jest-setup.js`

此文件用于全局配置和 mock 测试环境。关键设置包括：

- **Mock `Platform.OS`**:

  ```javascript
  jest.mock('expo-modules-core/src/Platform', () => ({
    OS: 'ios', // 或者 'android'，根据主要测试平台
  }));
  ```

  这解决了 `process.env.EXPO_OS` 未定义的警告。

- **Mock `react-native-paper` 的 `PaperProvider` (用于图标)**:

  ```javascript
  jest.mock('react-native-paper', () => {
    const React = require('react');
    const ReactNativePaper = jest.requireActual('react-native-paper');
    const { Text } = require('react-native');

    const MockIconProvider = (props) => {
      return React.createElement(
        Text,
        { testID: props.testID || `mock-icon-${props.name}` },
        `Icon(${props.name})`
      );
    };

    return {
      ...ReactNativePaper,
      PaperProvider: ({ children, theme, settings = {} }) => {
        const newSettings = {
          ...settings,
          icon: settings.icon || MockIconProvider,
        };
        return (
          <ReactNativePaper.PaperProvider theme={theme} settings={newSettings}>
            {children}
          </ReactNativePaper.PaperProvider>
        );
      },
    };
  });
  ```

  这对于测试使用了 `react-native-paper` 图标的组件至关重要，避免了因缺少实际图标字体/库而导致的错误和警告。

- **Mock `@expo/vector-icons/MaterialCommunityIcons`**:
  ```javascript
  jest.mock('@expo/vector-icons/MaterialCommunityIcons', () => {
    const React = require('react');
    const { Text } = require('react-native');
    return (props) =>
      React.createElement(
        Text,
        { testID: props.testID || `mci-${props.name}` },
        `MCI(${props.name})`
      );
  });
  ```
  作为备用 mock，确保直接使用此图标库的组件也能在测试中正常渲染一个占位符。

### c. `babel.config.js`

确保 `babel.config.js` 使用 `babel-preset-expo`，它处理了大部分 React Native 和 Expo 特有的代码转换。

```javascript
module.exports = function (api) {
  api.cache(true);
  return {
    presets: ['babel-preset-expo'],
    // ... 其他插件或配置
  };
};
```

## 3. 如何运行测试

使用 `pnpm` 作为包管理工具，可以通过以下命令运行测试：

- **运行所有测试 (单次运行)**:
  ```bash
  pnpm test --watchAll=false
  ```
- **运行所有测试 (监听模式)**:
  ```bash
  pnpm test
  ```
- **运行指定测试文件**:
  ```bash
  pnpm test <文件路径>
  # 例如: pnpm test components/ui/Avatar/Avatar.test.tsx
  ```
- **清除 Jest 缓存**:
  如果遇到奇怪的缓存问题，可以尝试清除缓存：
  ```bash
  pnpm test --clearCache
  ```

## 4. 常见问题及解决思路

在配置和编写测试过程中，可能会遇到一些常见问题：

- **`SyntaxError: Cannot use import statement outside a module` 或其他来自 `node_modules` 的语法错误**:

  - **原因**: Jest 默认不转换 `node_modules` 中的代码。许多 React Native 或 Expo 相关的库使用了 Jest 可能无法直接理解的较新 JavaScript 语法或 JSX。
  - **解决方案**: 在 `jest.config.js` 中设置 `transformIgnorePatterns: []`，强制 Babel 转换所有模块。

- **`process.env.EXPO_OS` 未定义警告**:

  - **原因**: `expo-modules-core` 中的某些代码依赖此环境变量。
  - **解决方案**: 在 `jest-setup.js` 中 mock `expo-modules-core/src/Platform` 并提供 `OS` 属性。

- **`act(...)` 警告**:

  - **原因**: React 组件中的某些操作（如异步数据获取后的状态更新、动画、图标加载）导致了在测试 `act` 函数包裹之外的状态更新。
  - **解决方案**:
    - 对于组件内部 `useEffect` 中包含的异步操作（如 API 调用后 `setState`），在测试中渲染这些组件时，应使用 React Native Testing Library 提供的 `waitFor` 工具来等待这些异步操作完成和状态更新。
    - 对于 `react-native-paper` 组件（尤其是包含图标的组件）产生的 `act` 警告，关键在于正确 mock 图标的渲染。通过在 `jest-setup.js` 中 mock `PaperProvider` 并注入一个简单的、同步的 mock 图标组件，可以有效避免这类问题。直接 mock `@expo/vector-icons` 库也有帮助。

- **访问组件 props (如 `disabled`, `loading`) 失败**:

  - **原因**: 对于一些第三方 UI 库的组件 (如 `react-native-paper` 的 `Button`)，通过 `getByTestId(...).props.X` 直接访问其状态 prop 可能不如预期。
  - **解决方案**:
    - 优先使用 React Native Testing Library 提供的特定匹配器，例如用 `toBeDisabled()` (或检查 `accessibilityState.disabled`) 来判断禁用状态。
    - 对于加载状态，可以检查 UI 是否如预期那样发生了变化（例如，`react-native-paper` 的 `Button` 在加载时会显示一个 `ActivityIndicator`，可以尝试查询这个指示器的存在）。

- **`testID` 未按预期工作**:
  - **原因**: `testID` 可能没有被正确地从自定义组件传递到底层的原生或第三方组件。
  - **解决方案**: 确保你的自定义组件接受 `testID` prop，并将其正确地传递给你希望在测试中定位的内部组件。

本指南应能帮助您理解和参与到项目的测试工作中。随着项目的进展，我们可能会遇到新的挑战，届时可以共同更新此文档。
