import React from 'react';
import { View, ActivityIndicator } from 'react-native';
import { Button, Text } from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import AiSuggestionCard from '@/components/creation/AiSuggestionCard';

interface AISuggestionsSectionProps {
  onFetchSuggestions: () => void;
  loadingSuggestions: boolean;
  showSuggestions: boolean;
  suggestions: string[];
  onSelectSuggestion: (suggestion: string) => void;
  disabled?: boolean;
}

export default function AISuggestionsSection({
  onFetchSuggestions,
  loadingSuggestions,
  showSuggestions,
  suggestions,
  onSelectSuggestion,
  disabled = false,
}: AISuggestionsSectionProps) {
  const { t } = useTranslation();
  const theme = usePaperTheme();

  return (
    <View>
      <Button
        mode="contained-tonal"
        onPress={onFetchSuggestions}
        disabled={loadingSuggestions || disabled}
        loading={loadingSuggestions}
        icon="lightbulb-outline"
        style={{
          paddingVertical: 8,
          marginBottom: 24,
          borderRadius: 24, // MD3 推荐的按钮圆角
          elevation: 1,
        }}
        labelStyle={{
          fontSize: 16,
          fontWeight: '600',
          paddingVertical: 4,
        }}
        buttonColor={theme.colors.secondaryContainer}
        textColor={theme.colors.onSecondaryContainer}
      >
        {t('storyForm.getAISuggestions', '获取 AI 建议')}
      </Button>

      {/* Loading State */}
      {showSuggestions && loadingSuggestions && (
        <View
          style={{
            alignItems: 'center',
            paddingVertical: 32,
            backgroundColor: theme.colors.surfaceVariant,
            borderRadius: 16,
            marginTop: 16,
          }}
        >
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text
            variant="bodyMedium"
            style={{
              marginTop: 16,
              color: theme.colors.onSurfaceVariant,
            }}
          >
            {t('aiSuggestions.loading', '正在生成建议...')}
          </Text>
        </View>
      )}

      {/* Suggestions List */}
      {showSuggestions && !loadingSuggestions && suggestions.length > 0 && (
        <View style={{ marginTop: 16 }}>
          <Text
            variant="titleMedium"
            style={{
              marginBottom: 16,
              color: theme.colors.onSurface,
              fontWeight: '600',
            }}
          >
            {t('aiSuggestions.title', 'AI 建议:')}
          </Text>
          {suggestions.map((suggestion, index) => (
            <AiSuggestionCard
              key={index}
              suggestion={suggestion}
              onSelect={onSelectSuggestion}
            />
          ))}
        </View>
      )}

      {/* Empty State */}
      {showSuggestions && !loadingSuggestions && suggestions.length === 0 && (
        <View
          style={{
            alignItems: 'center',
            paddingVertical: 32,
            backgroundColor: theme.colors.surfaceVariant,
            borderRadius: 16,
            marginTop: 16,
          }}
        >
          <Text
            variant="bodyMedium"
            style={{
              textAlign: 'center',
              color: theme.colors.onSurfaceVariant,
              lineHeight: 24,
            }}
          >
            {t(
              'aiSuggestions.noSuggestions',
              '暂时没有合适的建议，尝试修改你的输入或稍后再试。'
            )}
          </Text>
        </View>
      )}
    </View>
  );
}
