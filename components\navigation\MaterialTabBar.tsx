import React from 'react';
import { View } from 'react-native';
import { BottomNavigation } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import { useTranslation } from 'react-i18next';
import { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import { CommonActions } from '@react-navigation/native';

// 图标映射到 Material Community Icons
const getTabIcon = (
  routeName: string
): { focusedIcon: string; unfocusedIcon?: string } => {
  switch (routeName) {
    case 'home':
      return { focusedIcon: 'compass', unfocusedIcon: 'compass-outline' };
    case 'create':
      return {
        focusedIcon: 'pencil-plus',
        unfocusedIcon: 'pencil-plus-outline',
      };
    case 'stories':
      return {
        focusedIcon: 'book-open-page-variant',
        unfocusedIcon: 'book-open-page-variant-outline',
      };
    case 'social':
      return {
        focusedIcon: 'account-group',
        unfocusedIcon: 'account-group-outline',
      };
    case 'profile':
      return {
        focusedIcon: 'account-circle',
        unfocusedIcon: 'account-circle-outline',
      };
    default:
      return {
        focusedIcon: 'help-circle',
        unfocusedIcon: 'help-circle-outline',
      };
  }
};

// 获取标签文本的翻译key
const getTabLabelKey = (routeName: string): string => {
  switch (routeName) {
    case 'home':
      return 'tabs.home';
    case 'create':
      return 'tabs.create';
    case 'stories':
      return 'tabs.stories';
    case 'social':
      return 'tabs.social';
    case 'profile':
      return 'tabs.profile';
    default:
      return routeName;
  }
};

export function MaterialTabBar({
  state,
  descriptors,
  navigation,
}: BottomTabBarProps) {
  const theme = usePaperTheme();
  const { t } = useTranslation();

  // 构建 BottomNavigation 需要的 routes 数组
  const routes = state.routes.map((route) => {
    const { focusedIcon, unfocusedIcon } = getTabIcon(route.name);
    const labelKey = getTabLabelKey(route.name);

    return {
      key: route.key,
      title: t(labelKey, route.name), // 使用翻译，fallback到route名称
      focusedIcon,
      unfocusedIcon,
      // 可以在这里添加badge逻辑
      // badge: route.name === 'social' ? true : false,
    };
  });

  // 处理标签切换
  const handleIndexChange = (index: number) => {
    const route = state.routes[index];
    const isFocused = state.index === index;

    if (!isFocused) {
      // 使用 CommonActions.navigate 来确保正确的导航行为
      navigation.dispatch(
        CommonActions.navigate({
          name: route.name,
          merge: true,
        })
      );
    }
  };

  return (
    <View
      style={{
        backgroundColor: theme.colors.surface,
        elevation: 0, // MD3风格：移除阴影
        borderTopWidth: 1,
        borderTopColor: theme.colors.outlineVariant,
      }}
    >
      <BottomNavigation
        navigationState={{ index: state.index, routes }}
        onIndexChange={handleIndexChange}
        renderScene={() => null} // 我们不需要渲染场景，Expo Router会处理
        activeColor={theme.colors.onSecondaryContainer}
        inactiveColor={theme.colors.onSurfaceVariant}
        barStyle={{
          backgroundColor: theme.colors.surface,
          elevation: 0,
          borderTopWidth: 0, // 已在外层View处理
          height: 80, // MD3推荐高度
          paddingBottom: 8,
          paddingTop: 12,
        }}
        sceneAnimationEnabled={false} // 禁用场景动画，因为我们不渲染场景
        compact={false} // 确保所有标签都显示文本
        shifting={false} // 禁用shifting效果以符合MD3
        labeled={true} // 确保显示标签文本
        safeAreaInsets={{ bottom: 0 }} // 让Expo Router处理安全区域
        theme={{
          colors: {
            secondaryContainer: theme.colors.secondaryContainer,
            onSecondaryContainer: theme.colors.onSecondaryContainer,
            surface: theme.colors.surface,
            onSurface: theme.colors.onSurface,
            onSurfaceVariant: theme.colors.onSurfaceVariant,
          },
        }}
      />
    </View>
  );
}
