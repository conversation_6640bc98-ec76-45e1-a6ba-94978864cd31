import React from 'react';
import { View } from 'react-native';
import { Text, ActivityIndicator } from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { usePaperTheme } from '@/hooks/usePaperTheme';

interface ListFooterProps {
  isLoading: boolean;
}

export function ListFooter({ isLoading }: ListFooterProps) {
  const theme = usePaperTheme();
  const { t } = useTranslation();

  if (!isLoading) return null;

  return (
    <View
      style={{
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        padding: theme.spacing?.md || 16,
      }}
    >
      <ActivityIndicator size="small" />
      <Text
        variant="bodyMedium"
        style={{
          marginLeft: theme.spacing?.sm || 8,
          color: theme.colors.onSurfaceVariant,
        }}
      >
        {t('social.feed.loadingMore', '加载更多...')}
      </Text>
    </View>
  );
}
