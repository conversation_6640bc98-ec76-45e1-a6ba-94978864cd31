import React from 'react';
import { ScrollView } from 'react-native';
import { Surface, Appbar, Icon } from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import { Profile } from '@/api/profiles';
import { Story as ApiStory } from '@/api/stories';
import { ProfileHeader } from './ProfileHeader';
import { ProfileStats } from './ProfileStats';
import { ProfileActions } from './ProfileActions';
import { MyStoriesSection } from './MyStoriesSection';

interface ProfileScreenContentProps {
  profile: Profile;
  userStories: ApiStory[];
  storiesLoading: boolean;
  storiesError: string | null;
  onEditProfile: () => void;
  onShareProfile: () => void;
  onStoryPress: (storyId: string) => void;
}

export function ProfileScreenContent({
  profile,
  userStories,
  storiesLoading,
  storiesError,
  onEditProfile,
  onShareProfile,
  onStoryPress,
}: ProfileScreenContentProps) {
  const { t } = useTranslation();
  const theme = usePaperTheme();

  return (
    <Surface style={{ flex: 1 }}>
      {/* App Bar with title and settings */}
      <Appbar.Header>
        <Appbar.Content title={t('profile.title', '我的')} />
        <Appbar.Action
          icon="cog"
          onPress={() => console.log('Settings pressed')}
        />
      </Appbar.Header>

      {/* Scrollable content */}
      <ScrollView
        contentContainerStyle={{
          paddingBottom: theme.spacing?.lg || 24,
        }}
      >
        <ProfileHeader
          name={
            profile.full_name ||
            profile.username ||
            t('profile.unnamedUser', 'Unnamed User')
          }
          bio={profile.bio || t('profile.noBioPlaceholder', 'No bio yet.')}
          avatarUrl={profile.avatar_url || undefined}
          isPremium={false}
        />
        <ProfileStats posts={15} followers={234} following={189} />
        <ProfileActions
          onEditProfile={onEditProfile}
          onShareProfile={onShareProfile}
        />
        <MyStoriesSection
          stories={userStories}
          onStoryPress={onStoryPress}
          loading={storiesLoading}
          error={storiesError}
        />
      </ScrollView>
    </Surface>
  );
}
