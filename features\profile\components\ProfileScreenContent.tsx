import React from 'react';
import { ScrollView } from 'react-native';
import { Surface, Appbar, Icon } from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import { Profile } from '@/api/profiles';
import { Story as ApiStory } from '@/api/stories';
import { ProfileHeader } from './ProfileHeader';
import { ProfileStats } from './ProfileStats';
import { ProfileActions } from './ProfileActions';
import { MyStoriesSection } from './MyStoriesSection';

interface ProfileScreenContentProps {
  profile: Profile;
  userStories: ApiStory[];
  storiesLoading: boolean;
  storiesError: string | null;
  onEditProfile: () => void;
  onShareProfile: () => void;
  onStoryPress: (storyId: string) => void;
}

export function ProfileScreenContent({
  profile,
  userStories,
  storiesLoading,
  storiesError,
  onEditProfile,
  onShareProfile,
  onStoryPress,
}: ProfileScreenContentProps) {
  const { t } = useTranslation();
  const theme = usePaperTheme();

  return (
    <Surface style={{ flex: 1 }}>
      {/* Header is now handled by _layout.tsx */}

      {/* Scrollable content with MD3 spacing */}
      <ScrollView
        style={{ flex: 1 }}
        contentContainerStyle={{
          paddingBottom: theme.spacing?.xxl || 48,
        }}
        showsVerticalScrollIndicator={false}
      >
        {/* Hero Section - Profile Header */}
        <Surface
          style={{
            backgroundColor: theme.colors.surface,
            paddingTop: theme.spacing?.lg || 24,
          }}
          elevation={0}
        >
          <ProfileHeader
            name={
              profile.full_name ||
              profile.username ||
              t('profile.unnamedUser', 'Unnamed User')
            }
            bio={profile.bio || t('profile.noBioPlaceholder', 'No bio yet.')}
            avatarUrl={profile.avatar_url || undefined}
            isPremium={false}
          />
        </Surface>

        {/* Stats Section with proper spacing */}
        <Surface
          style={{
            backgroundColor: theme.colors.background,
            paddingVertical: theme.spacing?.lg || 24,
          }}
          elevation={0}
        >
          <ProfileStats posts={15} followers={234} following={189} />
        </Surface>

        {/* Actions Section */}
        <Surface
          style={{
            backgroundColor: theme.colors.surface,
            paddingVertical: theme.spacing?.md || 16,
          }}
          elevation={0}
        >
          <ProfileActions
            onEditProfile={onEditProfile}
            onShareProfile={onShareProfile}
          />
        </Surface>

        {/* Stories Section with visual separation */}
        <Surface
          style={{
            backgroundColor: theme.colors.background,
            paddingTop: theme.spacing?.xl || 32,
            minHeight: 320, // Ensure enough space for horizontal scroll
          }}
          elevation={0}
        >
          <MyStoriesSection
            stories={userStories}
            onStoryPress={onStoryPress}
            loading={storiesLoading}
            error={storiesError}
          />
        </Surface>
      </ScrollView>
    </Surface>
  );
}
