import React from 'react';
import { Surface } from 'react-native-paper';
import ProfileScreenContent from '@/features/profile/screens/ProfileScreen';
import { usePaperTheme } from '@/hooks/usePaperTheme';

// This route file now simply imports and renders the feature screen
// It uses Paper Surface for MD3 compliance
export default function ProfileRoute() {
  const theme = usePaperTheme();
  return (
    <Surface style={{ flex: 1, backgroundColor: theme.colors.background }}>
      {/* Header is now handled by _layout.tsx */}
      <ProfileScreenContent />
    </Surface>
  );
}

/*
// Original placeholder content (commented out)
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAppTheme } from '@/hooks/useAppTheme';
import { useTranslation } from 'react-i18next';

export default function ProfileScreen_Placeholder() {
  const { t } = useTranslation();
  const theme = useAppTheme();

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: theme.colors.background, justifyContent: 'center', alignItems: 'center' }}>
      <Text style={{ color: theme.colors.text, fontSize: 20 }}>
        {t('profile')} {t('testPlaceholder')}
      </Text>
    </SafeAreaView>
  );
}
*/
