import React from 'react';
import { View } from 'react-native';
import { Card, Text, Icon, TouchableRipple } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import { User } from '@/types/user';
import { Avatar } from '@/components/ui/Avatar';

interface ActivityItem {
  id: string;
  type: 'comment' | 'like' | 'branch';
  user: User;
  storyTitle: string;
  content: string;
  time: string;
}

interface ActivityFeedItemProps {
  item: ActivityItem;
  onPress?: () => void;
}

export default function ActivityFeedItem({
  item,
  onPress,
}: ActivityFeedItemProps) {
  const theme = usePaperTheme();

  // 获取 Material Community Icon 名称
  const getIconName = (type: string): string => {
    switch (type) {
      case 'comment':
        return 'message-outline';
      case 'like':
        return 'heart';
      case 'branch':
        return 'source-branch';
      default:
        return 'help';
    }
  };

  // 获取图标颜色
  const getIconColor = (type: string): string => {
    switch (type) {
      case 'comment':
        return theme.colors.primary;
      case 'like':
        return theme.colors.error;
      case 'branch':
        return '#30D158'; // 保持绿色
      default:
        return theme.colors.onSurface;
    }
  };

  return (
    <Card
      mode="elevated"
      style={{
        marginVertical: theme.spacing?.xs || 4,
        marginHorizontal: theme.spacing?.sm || 8,
      }}
    >
      <TouchableRipple onPress={onPress} disabled={!onPress}>
        <Card.Content style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Avatar
            uri={item.user.avatar}
            size={40}
            username={item.user.displayName}
          />

          <View style={{ flex: 1, marginLeft: theme.spacing?.sm || 8 }}>
            <View
              style={{ flexDirection: 'row', justifyContent: 'space-between' }}
            >
              <Text variant="titleSmall">{item.user.displayName}</Text>
              <Text
                variant="bodySmall"
                style={{ color: theme.colors.onSurfaceVariant }}
              >
                {item.time}
              </Text>
            </View>

            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginTop: theme.spacing?.xs || 4,
              }}
            >
              <Icon
                source={getIconName(item.type)}
                size={16}
                color={getIconColor(item.type)}
              />
              <Text
                variant="bodyMedium"
                style={{ marginLeft: theme.spacing?.xs || 4 }}
                numberOfLines={1}
              >
                {item.content}
              </Text>
            </View>

            <Text
              variant="bodySmall"
              style={{
                color: theme.colors.onSurfaceVariant,
                marginTop: theme.spacing?.xs || 4,
              }}
              numberOfLines={1}
            >
              《{item.storyTitle}》
            </Text>
          </View>
        </Card.Content>
      </TouchableRipple>
    </Card>
  );
}
