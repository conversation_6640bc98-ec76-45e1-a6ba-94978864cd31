import React, { memo } from 'react';
import { View, Platform } from 'react-native';
import { Card, Text, Chip, Icon } from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import { Story as AppStory } from '@/types/story';
import { Story as ApiStory } from '@/api/stories';
import { BlurView } from 'expo-blur';

// 支持两种不同的Story类型
type StoryUnion = AppStory | ApiStory;

interface StoryPreviewCardProps {
  story: StoryUnion;
  onPress?: () => void;
}

/**
 * 故事预览卡片组件，展示故事的封面图片、标题和主题标签
 */
const StoryPreviewCard = memo(({ story, onPress }: StoryPreviewCardProps) => {
  const theme = usePaperTheme();

  // 适配不同的Story类型
  const getTitle = (): string => {
    if ('title' in story) return story.title;
    return '';
  };

  const getCoverImage = (): string => {
    if ('coverImage' in story) return story.coverImage;
    if ('cover_image_url' in story && story.cover_image_url)
      return story.cover_image_url;
    // 默认封面图片
    return 'https://picsum.photos/200/300';
  };

  const getThemes = (): string[] => {
    if ('theme' in story && Array.isArray(story.theme)) return story.theme;
    if ('tags' in story && Array.isArray(story.tags)) return story.tags || [];
    return [];
  };

  // 获取第一个主题标签
  const themes = getThemes();
  const title = getTitle();
  const coverImage = getCoverImage();

  return (
    <Card
      mode="elevated"
      onPress={onPress}
      style={{
        width: 200,
        height: 280,
        marginRight: theme.spacing?.md || 16,
        borderRadius: theme.roundness || 12,
        overflow: 'hidden',
      }}
    >
      {/* Cover Image */}
      <Card.Cover
        source={{ uri: coverImage }}
        style={{
          height: '100%',
          borderRadius: 0,
        }}
      />

      {/* Gradient Overlay with Content */}
      <LinearGradient
        colors={['transparent', 'rgba(0,0,0,0.7)', 'rgba(0,0,0,0.85)']}
        style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          height: '50%',
          justifyContent: 'flex-end',
          padding: theme.spacing?.md || 16,
        }}
      >
        {/* Title */}
        {title && (
          <Text
            variant="titleMedium"
            numberOfLines={2}
            style={{
              color: '#FFFFFF',
              marginBottom: theme.spacing?.sm || 8,
              fontWeight: 'bold',
            }}
          >
            {title}
          </Text>
        )}

        {/* Bottom Row with Theme and Arrow */}
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          {/* Theme Tags */}
          <View style={{ flex: 1 }}>
            {themes.slice(0, 1).map((themeName) => (
              <Chip
                key={themeName}
                mode="flat"
                compact
                style={{
                  backgroundColor: 'rgba(255,255,255,0.2)',
                  alignSelf: 'flex-start',
                }}
                textStyle={{
                  color: '#FFFFFF',
                  fontSize: 12,
                }}
              >
                {themeName}
              </Chip>
            ))}
          </View>

          {/* Arrow Icon */}
          {Platform.OS === 'ios' ? (
            <BlurView
              intensity={20}
              style={{
                borderRadius: 16,
                padding: 8,
                marginLeft: theme.spacing?.sm || 8,
              }}
            >
              <Icon source="arrow-right" size={16} color="#FFFFFF" />
            </BlurView>
          ) : (
            <View
              style={{
                backgroundColor: 'rgba(255,255,255,0.2)',
                borderRadius: 16,
                padding: 8,
                marginLeft: theme.spacing?.sm || 8,
              }}
            >
              <Icon source="arrow-right" size={16} color="#FFFFFF" />
            </View>
          )}
        </View>
      </LinearGradient>
    </Card>
  );
});

export default StoryPreviewCard;
