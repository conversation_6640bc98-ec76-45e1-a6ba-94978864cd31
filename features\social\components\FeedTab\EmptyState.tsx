import React from 'react';
import { View } from 'react-native';
import { Text, Button, Icon } from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { usePaperTheme } from '@/hooks/usePaperTheme';

interface EmptyStateProps {
  onDiscoverPress?: () => void;
}

export function EmptyState({ onDiscoverPress }: EmptyStateProps) {
  const theme = usePaperTheme();
  const { t } = useTranslation();

  return (
    <View
      style={{
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: theme.spacing?.lg || 24,
      }}
    >
      <Icon
        source="account-group"
        size={60}
        color={theme.colors.onSurfaceVariant}
      />
      <Text
        variant="titleMedium"
        style={{
          marginTop: theme.spacing?.md || 16,
          textAlign: 'center',
          color: theme.colors.onSurface,
        }}
      >
        {t('social.feed.emptyStateTitle', '暂无动态')}
      </Text>
      <Text
        variant="bodyMedium"
        style={{
          marginTop: theme.spacing?.sm || 8,
          textAlign: 'center',
          color: theme.colors.onSurfaceVariant,
          maxWidth: 280,
        }}
      >
        {t(
          'social.feed.empty',
          '暂无动态。关注更多用户以在此处查看他们的故事。'
        )}
      </Text>
      <Button
        mode="contained"
        icon="plus"
        onPress={onDiscoverPress}
        style={{ marginTop: theme.spacing?.lg || 24 }}
      >
        {t('social.feed.discoverUsers', '发现用户')}
      </Button>
    </View>
  );
}
