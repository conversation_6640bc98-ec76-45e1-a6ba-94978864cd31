import React from 'react';
import { View } from 'react-native';
import { Text } from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { usePaperTheme } from '@/hooks/usePaperTheme';

export default function CreateStoryHeader() {
  const { t } = useTranslation();
  const theme = usePaperTheme();

  return (
    <View>
      <Text
        variant="headlineMedium"
        style={{
          marginBottom: 8,
          color: theme.colors.onSurface,
          fontWeight: '600',
        }}
      >
        {t('createStoryTitle', '创建新故事')}
      </Text>
      <Text
        variant="bodyLarge"
        style={{
          color: theme.colors.onSurfaceVariant,
          lineHeight: 24,
        }}
      >
        {t('storyForm.createDescription', '开始你的创作之旅，写下你的故事')}
      </Text>
    </View>
  );
}
