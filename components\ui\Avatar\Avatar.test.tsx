import React from 'react';
import { render } from '@testing-library/react-native';
import { PaperProvider } from 'react-native-paper';
import { Avatar } from './index';
import { paperLightTheme } from '@/lib/theme/paperThemes';

// Wrapper component to provide theme context
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <PaperProvider theme={paperLightTheme}>{children}</PaperProvider>
);

describe('Avatar', () => {
  it('renders Avatar.Image when uri is provided', () => {
    const testUri = 'https://example.com/avatar.jpg';
    const { getByTestId } = render(
      <TestWrapper>
        <Avatar
          uri={testUri}
          username="John Doe"
          size={50}
          testID="user-avatar"
        />
      </TestWrapper>
    );

    // Check for the specific testID passed to Avatar.Image
    expect(getByTestId('user-avatar-image')).toBeTruthy();
  });

  it('renders Avatar.Text when no uri is provided', () => {
    const { getByText, getByTestId } = render(
      <TestWrapper>
        <Avatar username="John Doe" size={50} testID="user-avatar" />
      </TestWrapper>
    );

    // Should render initials "JD" for "John Doe"
    expect(getByText('JD')).toBeTruthy();
    expect(getByTestId('user-avatar-text')).toBeTruthy();
  });

  it('renders Avatar.Text with single initial for single name', () => {
    const { getByText, getByTestId } = render(
      <TestWrapper>
        <Avatar username="John" size={50} testID="user-avatar" />
      </TestWrapper>
    );

    // Should render initial "J" for "John"
    expect(getByText('J')).toBeTruthy();
    expect(getByTestId('user-avatar-text')).toBeTruthy();
  });

  it('renders Avatar.Text with "?" when no username is provided', () => {
    const { getByText, getByTestId } = render(
      <TestWrapper>
        <Avatar size={50} testID="user-avatar" />
      </TestWrapper>
    );

    // Should render "?" when no username
    expect(getByText('?')).toBeTruthy();
    expect(getByTestId('user-avatar-text')).toBeTruthy();
  });

  it('uses default size when not specified', () => {
    const { getByText, getByTestId } = render(
      <TestWrapper>
        <Avatar username="Test User" testID="user-avatar" />
      </TestWrapper>
    );

    // Should render with default size (component should still render)
    expect(getByText('TU')).toBeTruthy();
    expect(getByTestId('user-avatar-text')).toBeTruthy(); // Ensure text avatar is rendered
  });

  it('handles empty username gracefully', () => {
    const { getByText, getByTestId } = render(
      <TestWrapper>
        <Avatar username="" size={50} testID="user-avatar" />
      </TestWrapper>
    );

    // Should render "?" for empty username
    expect(getByText('?')).toBeTruthy();
    expect(getByTestId('user-avatar-text')).toBeTruthy();
  });

  it('handles username with multiple spaces', () => {
    const { getByText, getByTestId } = render(
      <TestWrapper>
        <Avatar username="John   Middle   Doe" size={50} testID="user-avatar" />
      </TestWrapper>
    );

    // Should render "JD" (first and last name initials)
    expect(getByText('JD')).toBeTruthy();
    expect(getByTestId('user-avatar-text')).toBeTruthy();
  });

  it('prefers image over text when both uri and username are provided', () => {
    const testUri = 'https://example.com/avatar.jpg';
    const { queryByText, getByTestId, queryByTestId } = render(
      <TestWrapper>
        <Avatar
          uri={testUri}
          username="John Doe"
          size={50}
          testID="user-avatar"
        />
      </TestWrapper>
    );

    // Image avatar should be present
    expect(getByTestId('user-avatar-image')).toBeTruthy();
    // Text avatar should not be present
    expect(queryByTestId('user-avatar-text')).toBeNull();
    expect(queryByText('JD')).toBeNull();
  });
});
