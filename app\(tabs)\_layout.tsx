import { Tabs, useRouter } from 'expo-router';
import { TouchableOpacity } from 'react-native';
import { Icon } from 'react-native-paper';

import { usePaperTheme } from '@/hooks/usePaperTheme';
import { useTranslation } from 'react-i18next';
import { MaterialTabBar } from '@/components/navigation/MaterialTabBar';

export default function TabLayout() {
  const theme = usePaperTheme();
  const router = useRouter();
  const { t } = useTranslation();

  return (
    <Tabs
      tabBar={(props) => <MaterialTabBar {...props} />}
      screenOptions={{
        headerShown: false,
      }}
    >
      <Tabs.Screen
        name="home"
        options={{
          title: t('tabs.home'),
        }}
      />
      <Tabs.Screen
        name="create"
        options={{
          title: t('tabs.create'),
        }}
      />
      <Tabs.Screen
        name="stories"
        options={{
          title: t('tabs.stories'),
        }}
      />
      <Tabs.Screen
        name="social"
        options={{
          title: t('tabs.social'),
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: t('tabs.profile'),
          headerShown: true,
          headerStyle: {
            backgroundColor: theme.colors.surface,
            elevation: 0,
            borderBottomWidth: 1,
            borderBottomColor: theme.colors.outline,
          },
          headerTitleStyle: {
            color: theme.colors.onSurface,
            fontFamily: theme.fonts?.titleLarge?.fontFamily || 'System',
            fontSize: theme.fonts?.titleLarge?.fontSize || 18,
            fontWeight: theme.fonts?.titleLarge?.fontWeight || '600',
          },
          headerRight: () => (
            <TouchableOpacity
              onPress={() => router.push('/(settings)')}
              style={{ marginRight: theme.spacing?.md || 16 }}
            >
              <Icon source="cog" size={24} color={theme.colors.onSurface} />
            </TouchableOpacity>
          ),
        }}
      />
    </Tabs>
  );
}
