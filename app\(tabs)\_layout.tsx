import { Tabs, useRouter } from 'expo-router';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import { useTranslation } from 'react-i18next';
import { MaterialTabBar } from '@/components/navigation/MaterialTabBar';
import { CustomAppbar } from '@/components/navigation/CustomAppbar';

export default function TabLayout() {
  const theme = usePaperTheme();
  const router = useRouter();
  const { t } = useTranslation();

  return (
    <Tabs
      tabBar={(props) => <MaterialTabBar {...props} />}
      screenOptions={{
        headerShown: true,
        header: (props) => <CustomAppbar {...props} />,
      }}
    >
      <Tabs.Screen
        name="home"
        options={{
          title: t('tabs.home'),
          subtitle: t('homeScreen.subtitle'),
        }}
      />
      <Tabs.Screen
        name="create"
        options={{
          title: t('tabs.create'),
          headerShown: false, // Create page handles its own header
        }}
      />
      <Tabs.Screen
        name="stories"
        options={{
          title: t('tabs.stories'),
        }}
      />
      <Tabs.Screen
        name="social"
        options={{
          title: t('tabs.social'),
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: t('tabs.profile'),
        }}
      />
    </Tabs>
  );
}
