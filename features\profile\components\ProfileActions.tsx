import React from 'react';
import { View } from 'react-native';
import { Button } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import { useTranslation } from 'react-i18next';

interface ProfileActionsProps {
  onEditProfile?: () => void;
  onShareProfile?: () => void;
}

export function ProfileActions({
  onEditProfile,
  onShareProfile,
}: ProfileActionsProps) {
  const theme = usePaperTheme();
  const { t } = useTranslation();

  return (
    <View
      style={{
        flexDirection: 'row',
        justifyContent: 'space-around',
        paddingVertical: theme.spacing?.lg || 24,
        paddingHorizontal: theme.spacing?.md || 16,
        gap: theme.spacing?.md || 16,
      }}
    >
      {onEditProfile && (
        <Button
          mode="outlined"
          onPress={onEditProfile}
          style={{ flex: 1 }}
          icon="pencil"
        >
          {t('profile.editProfile', '编辑资料')}
        </Button>
      )}
      {onShareProfile && (
        <Button
          mode="contained-tonal"
          onPress={onShareProfile}
          style={{ flex: 1 }}
          icon="share"
        >
          {t('profile.shareProfile', '分享')}
        </Button>
      )}
    </View>
  );
}
