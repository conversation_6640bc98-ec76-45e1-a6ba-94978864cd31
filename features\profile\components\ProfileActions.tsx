import React from 'react';
import { View } from 'react-native';
import { Button } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import { useTranslation } from 'react-i18next';

interface ProfileActionsProps {
  onEditProfile?: () => void;
  onShareProfile?: () => void;
}

export function ProfileActions({
  onEditProfile,
  onShareProfile,
}: ProfileActionsProps) {
  const theme = usePaperTheme();
  const { t } = useTranslation();

  return (
    <View
      style={{
        flexDirection: 'row',
        justifyContent: 'center',
        paddingVertical: theme.spacing?.lg || 24,
        paddingHorizontal: theme.spacing?.lg || 24,
        gap: theme.spacing?.lg || 24,
      }}
    >
      {onEditProfile && (
        <Button
          mode="filled"
          onPress={onEditProfile}
          style={{
            flex: 1,
            borderRadius: theme.roundness || 12,
          }}
          contentStyle={{
            paddingVertical: theme.spacing?.sm || 8,
          }}
          labelStyle={{
            fontSize: 16,
            fontWeight: '600',
            letterSpacing: 0.25,
          }}
          icon="pencil"
        >
          {t('profile.editProfile', '编辑资料')}
        </Button>
      )}
      {onShareProfile && (
        <Button
          mode="outlined"
          onPress={onShareProfile}
          style={{
            flex: 1,
            borderRadius: theme.roundness || 12,
            borderColor: theme.colors.outline,
          }}
          contentStyle={{
            paddingVertical: theme.spacing?.sm || 8,
          }}
          labelStyle={{
            fontSize: 16,
            fontWeight: '600',
            letterSpacing: 0.25,
            color: theme.colors.primary,
          }}
          icon="share"
        >
          {t('profile.shareProfile', '分享')}
        </Button>
      )}
    </View>
  );
}
