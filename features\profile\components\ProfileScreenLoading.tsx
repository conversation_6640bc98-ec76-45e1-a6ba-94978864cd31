import React from 'react';
import { Surface, ActivityIndicator } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';

export function ProfileScreenLoading() {
  const theme = usePaperTheme();

  return (
    <Surface
      style={{
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: theme.colors.background,
      }}
    >
      <ActivityIndicator size="large" />
    </Surface>
  );
}
