import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { PaperProvider } from 'react-native-paper';
import SortSelector, { SortOption } from './index';
import { paperLightTheme } from '@/lib/theme/paperThemes';

// Mock react-i18next
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string, defaultValue: string) => defaultValue,
  }),
}));

// Wrapper component to provide theme context
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <PaperProvider theme={paperLightTheme}>{children}</PaperProvider>
);

const mockOptions: SortOption[] = [
  { id: 'newest', label: 'Newest First' },
  { id: 'oldest', label: 'Oldest First' },
  { id: 'popular', label: 'Most Popular' },
];

describe('SortSelector', () => {
  it('renders with selected option displayed', () => {
    const mockOnSelect = jest.fn();
    const { getByText } = render(
      <TestWrapper>
        <SortSelector
          options={mockOptions}
          selectedId="popular"
          onSelect={mockOnSelect}
        />
      </TestWrapper>
    );

    expect(getByText('Most Popular')).toBeTruthy();
  });

  it('renders with label when provided', () => {
    const mockOnSelect = jest.fn();
    const { getByText } = render(
      <TestWrapper>
        <SortSelector
          options={mockOptions}
          selectedId="newest"
          onSelect={mockOnSelect}
          label="Sort by:"
        />
      </TestWrapper>
    );

    expect(getByText('Sort by:')).toBeTruthy();
    expect(getByText('Newest First')).toBeTruthy();
  });

  it('does not render label when not provided', () => {
    const mockOnSelect = jest.fn();
    const { queryByText } = render(
      <TestWrapper>
        <SortSelector
          options={mockOptions}
          selectedId="newest"
          onSelect={mockOnSelect}
        />
      </TestWrapper>
    );

    expect(queryByText('Sort by:')).toBeNull();
  });

  it('opens menu when button is pressed', () => {
    const mockOnSelect = jest.fn();
    const { getByText } = render(
      <TestWrapper>
        <SortSelector
          options={mockOptions}
          selectedId="newest"
          onSelect={mockOnSelect}
        />
      </TestWrapper>
    );

    // Press the button to open menu
    fireEvent.press(getByText('Newest First'));

    // Menu items should be visible
    expect(getByText('Oldest First')).toBeTruthy();
    expect(getByText('Most Popular')).toBeTruthy();
  });

  it('calls onSelect when menu item is pressed', () => {
    const mockOnSelect = jest.fn();
    const { getByText } = render(
      <TestWrapper>
        <SortSelector
          options={mockOptions}
          selectedId="newest"
          onSelect={mockOnSelect}
        />
      </TestWrapper>
    );

    // Open menu
    fireEvent.press(getByText('Newest First'));

    // Select different option
    fireEvent.press(getByText('Most Popular'));

    expect(mockOnSelect).toHaveBeenCalledWith('popular');
  });

  it('displays first option when selectedId is not found', () => {
    const mockOnSelect = jest.fn();
    const { getByText } = render(
      <TestWrapper>
        <SortSelector
          options={mockOptions}
          selectedId="nonexistent"
          onSelect={mockOnSelect}
        />
      </TestWrapper>
    );

    // Should display first option as fallback
    expect(getByText('Newest First')).toBeTruthy();
  });

  it('handles empty options array gracefully', () => {
    const mockOnSelect = jest.fn();
    const { queryByText } = render(
      <TestWrapper>
        <SortSelector options={[]} selectedId="" onSelect={mockOnSelect} />
      </TestWrapper>
    );

    // Should not crash and should render empty button
    expect(queryByText('Newest First')).toBeNull();
  });

  it('shows check icon for selected option in menu', () => {
    const mockOnSelect = jest.fn();
    const { getByText, getAllByText } = render(
      <TestWrapper>
        <SortSelector
          options={mockOptions}
          selectedId="popular"
          onSelect={mockOnSelect}
        />
      </TestWrapper>
    );

    // Open menu
    fireEvent.press(getByText('Most Popular'));

    // The selected option should have a check icon
    // Note: This test might need adjustment based on how react-native-paper
    // Menu.Item renders the trailingIcon internally
    // Instead of getByText, use getAllByText and check length or a specific item
    expect(getAllByText('Most Popular').length).toBeGreaterThanOrEqual(1);
  });

  it('closes menu after selection', () => {
    const mockOnSelect = jest.fn();
    const { getByText, queryByText } = render(
      <TestWrapper>
        <SortSelector
          options={mockOptions}
          selectedId="newest"
          onSelect={mockOnSelect}
        />
      </TestWrapper>
    );

    // Open menu
    fireEvent.press(getByText('Newest First'));
    expect(getByText('Most Popular')).toBeTruthy();

    // Select option
    fireEvent.press(getByText('Most Popular'));

    // Menu should close (items should not be visible)
    // Note: This behavior depends on react-native-paper Menu implementation
    expect(mockOnSelect).toHaveBeenCalledWith('popular');
  });
});
