import React from 'react';
import { render } from '@testing-library/react-native';
import { PaperProvider } from 'react-native-paper';
import { ProfileHeader } from './ProfileHeader';
import { paperLightTheme } from '@/lib/theme/paperThemes';

// Mock i18next
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string, defaultValue?: string) => defaultValue || key,
  }),
}));

// Wrapper component to provide theme context
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <PaperProvider theme={paperLightTheme}>
    {children}
  </PaperProvider>
);

describe('ProfileHeader', () => {
  it('renders correctly with basic props', () => {
    const { getByText } = render(
      <TestWrapper>
        <ProfileHeader
          name="John Doe"
          bio="Software Developer"
          isPremium={false}
        />
      </TestWrapper>
    );

    expect(getByText('<PERSON>')).toBeTruthy();
    expect(getByText('Software Developer')).toBeTruthy();
  });

  it('renders premium badge when isPremium is true', () => {
    const { getByText } = render(
      <TestWrapper>
        <ProfileHeader
          name="Premium User"
          bio="VIP Member"
          isPremium={true}
        />
      </TestWrapper>
    );

    expect(getByText('Premium User')).toBeTruthy();
    expect(getByText('premium')).toBeTruthy();
  });

  it('renders avatar icon when no avatarUrl is provided', () => {
    const { getByText } = render(
      <TestWrapper>
        <ProfileHeader
          name="Test User"
          bio="Test Bio"
          isPremium={false}
        />
      </TestWrapper>
    );

    expect(getByText('Test User')).toBeTruthy();
    expect(getByText('Test Bio')).toBeTruthy();
  });

  it('uses Paper theme correctly', () => {
    const { getByText } = render(
      <TestWrapper>
        <ProfileHeader
          name="Theme Test"
          bio="Testing theme integration"
          isPremium={false}
        />
      </TestWrapper>
    );

    // Should render without errors, indicating proper theme integration
    expect(getByText('Theme Test')).toBeTruthy();
  });
});
