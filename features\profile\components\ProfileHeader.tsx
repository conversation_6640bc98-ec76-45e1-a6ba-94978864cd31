import React from 'react';
import { View } from 'react-native';
import { Avatar, Text, Icon, Chip } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import { useTranslation } from 'react-i18next';

interface ProfileHeaderProps {
  name: string;
  bio: string;
  avatarUrl?: string;
  isPremium?: boolean;
}

export function ProfileHeader({
  name,
  bio,
  avatarUrl,
  isPremium,
}: ProfileHeaderProps) {
  const theme = usePaperTheme();
  const { t } = useTranslation();

  return (
    <View
      style={{
        alignItems: 'center',
        paddingVertical: theme.spacing?.xl || 32,
        paddingHorizontal: theme.spacing?.lg || 24,
      }}
    >
      {/* Avatar Container with MD3 spacing */}
      <View
        style={{
          position: 'relative',
          marginBottom: theme.spacing?.lg || 24,
        }}
      >
        {avatarUrl ? (
          <Avatar.Image
            size={96} // Larger avatar for better visual hierarchy
            source={{ uri: avatarUrl }}
          />
        ) : (
          <Avatar.Icon
            size={96}
            icon="account"
            style={{
              backgroundColor: theme.colors.primaryContainer,
            }}
          />
        )}

        {/* Premium Badge with better positioning */}
        {isPremium && (
          <Chip
            icon="crown"
            mode="flat"
            compact
            style={{
              position: 'absolute',
              top: -4,
              right: -4,
              backgroundColor: theme.colors.tertiary,
              elevation: 2,
            }}
            textStyle={{
              color: theme.colors.onTertiary,
              fontSize: 10,
              fontWeight: '600',
            }}
          >
            {t('premium')}
          </Chip>
        )}
      </View>

      {/* Name with MD3 typography */}
      <Text
        variant="headlineMedium"
        style={{
          color: theme.colors.onSurface,
          textAlign: 'center',
          marginBottom: theme.spacing?.sm || 8,
          fontWeight: '600',
          letterSpacing: 0.25,
        }}
      >
        {name}
      </Text>

      {/* Bio with improved readability */}
      <Text
        variant="bodyLarge"
        style={{
          color: theme.colors.onSurfaceVariant,
          textAlign: 'center',
          paddingHorizontal: theme.spacing?.lg || 24,
          lineHeight: 24,
          maxWidth: 280, // Limit line length for better readability
        }}
      >
        {bio}
      </Text>
    </View>
  );
}
