import React from 'react';
import { View } from 'react-native';
import { Avatar, Text, Icon, Chip } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import { useTranslation } from 'react-i18next';

interface ProfileHeaderProps {
  name: string;
  bio: string;
  avatarUrl?: string;
  isPremium?: boolean;
}

export function ProfileHeader({
  name,
  bio,
  avatarUrl,
  isPremium,
}: ProfileHeaderProps) {
  const theme = usePaperTheme();
  const { t } = useTranslation();

  return (
    <View
      style={{
        alignItems: 'center',
        paddingVertical: theme.spacing?.lg || 24,
        paddingHorizontal: theme.spacing?.md || 16,
      }}
    >
      {/* Avatar Container */}
      <View
        style={{
          position: 'relative',
          marginBottom: theme.spacing?.md || 16,
        }}
      >
        {avatarUrl ? (
          <Avatar.Image size={80} source={{ uri: avatarUrl }} />
        ) : (
          <Avatar.Icon size={80} icon="account" />
        )}

        {/* Premium Badge */}
        {isPremium && (
          <Chip
            icon="crown"
            mode="flat"
            compact
            style={{
              position: 'absolute',
              top: -8,
              right: -8,
              backgroundColor: theme.colors.tertiary,
            }}
            textStyle={{
              color: theme.colors.onTertiary,
              fontSize: 10,
            }}
          >
            {t('premium')}
          </Chip>
        )}
      </View>

      {/* Name */}
      <Text
        variant="headlineSmall"
        style={{
          color: theme.colors.onSurface,
          textAlign: 'center',
          marginBottom: theme.spacing?.sm || 8,
        }}
      >
        {name}
      </Text>

      {/* Bio */}
      <Text
        variant="bodyMedium"
        style={{
          color: theme.colors.onSurfaceVariant,
          textAlign: 'center',
          paddingHorizontal: theme.spacing?.md || 16,
        }}
      >
        {bio}
      </Text>
    </View>
  );
}
