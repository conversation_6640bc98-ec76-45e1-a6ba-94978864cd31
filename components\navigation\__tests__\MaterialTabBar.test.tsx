import React from 'react';
import { render } from '@testing-library/react-native';
import { PaperProvider } from 'react-native-paper';
import { MaterialTabBar } from '../MaterialTabBar';
import { BottomTabBarProps } from '@react-navigation/bottom-tabs';

// Mock react-i18next
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string, defaultValue?: string) => defaultValue || key,
  }),
}));

// Mock navigation
const mockNavigation = {
  dispatch: jest.fn(),
  navigate: jest.fn(),
  reset: jest.fn(),
  goBack: jest.fn(),
  isFocused: jest.fn(),
  canGoBack: jest.fn(),
  getId: jest.fn(),
  getParent: jest.fn(),
  getState: jest.fn(),
  setParams: jest.fn(),
  setOptions: jest.fn(),
  addListener: jest.fn(),
  removeListener: jest.fn(),
  emit: jest.fn(),
};

const mockState = {
  index: 0,
  routeNames: ['home', 'create', 'stories', 'social', 'profile'],
  routes: [
    { key: 'home-key', name: 'home', params: undefined },
    { key: 'create-key', name: 'create', params: undefined },
    { key: 'stories-key', name: 'stories', params: undefined },
    { key: 'social-key', name: 'social', params: undefined },
    { key: 'profile-key', name: 'profile', params: undefined },
  ],
  type: 'tab' as const,
  stale: false,
  key: 'tab-key',
  history: [],
};

const mockDescriptors = {
  'home-key': {
    navigation: mockNavigation,
    route: mockState.routes[0],
    options: { title: 'Home' },
    render: () => null,
  },
  'create-key': {
    navigation: mockNavigation,
    route: mockState.routes[1],
    options: { title: 'Create' },
    render: () => null,
  },
  'stories-key': {
    navigation: mockNavigation,
    route: mockState.routes[2],
    options: { title: 'Stories' },
    render: () => null,
  },
  'social-key': {
    navigation: mockNavigation,
    route: mockState.routes[3],
    options: { title: 'Social' },
    render: () => null,
  },
  'profile-key': {
    navigation: mockNavigation,
    route: mockState.routes[4],
    options: { title: 'Profile' },
    render: () => null,
  },
};

const renderWithProvider = (component: React.ReactElement) => {
  return render(<PaperProvider>{component}</PaperProvider>);
};

describe('MaterialTabBar', () => {
  const defaultProps: BottomTabBarProps = {
    state: mockState,
    descriptors: mockDescriptors,
    navigation: mockNavigation,
    insets: { top: 0, right: 0, bottom: 0, left: 0 },
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly with all tabs', () => {
    const { getAllByText } = renderWithProvider(
      <MaterialTabBar {...defaultProps} />
    );

    // 检查是否渲染了所有标签
    expect(getAllByText('home')[0]).toBeTruthy();
    expect(getAllByText('create')[0]).toBeTruthy();
    expect(getAllByText('stories')[0]).toBeTruthy();
    expect(getAllByText('social')[0]).toBeTruthy();
    expect(getAllByText('profile')[0]).toBeTruthy();
  });

  it('shows correct active tab', () => {
    const { getAllByText } = renderWithProvider(
      <MaterialTabBar {...defaultProps} />
    );

    // 第一个标签应该是活动的（index: 0）
    // We expect 'home' to be rendered and it should be the active one.
    // The visual indication of active is handled by Paper's BottomNavigation.
    expect(getAllByText('home')[0]).toBeTruthy();
  });

  it('handles different active tab index', () => {
    const propsWithDifferentIndex = {
      ...defaultProps,
      state: {
        ...mockState,
        index: 2, // stories tab
      },
    };

    const { getAllByText } = renderWithProvider(
      <MaterialTabBar {...propsWithDifferentIndex} />
    );

    expect(getAllByText('stories')[0]).toBeTruthy();
  });
});
