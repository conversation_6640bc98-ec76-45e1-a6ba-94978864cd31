import React from 'react';
import { View } from 'react-native';
import { Appbar, Text } from 'react-native-paper';
import { useRouter } from 'expo-router';
import { usePaperTheme } from '@/hooks/usePaperTheme';
// useTranslation removed as it's not needed in this component

interface CustomAppbarProps {
  navigation: any;
  route: any;
  options: any;
  back?: any;
}

export function CustomAppbar({
  navigation,
  route,
  options,
  back,
}: CustomAppbarProps) {
  const theme = usePaperTheme();
  const router = useRouter();

  // Get title from options or route name
  const title = options.title || route.name;

  // Handle subtitle for specific routes (like home)
  const subtitle = options.subtitle;

  // Create custom title content to handle subtitle
  const titleContent = subtitle ? (
    <View>
      <Text
        variant="titleLarge"
        style={{
          color: theme.colors.onSurface,
          fontWeight: '600',
          letterSpacing: 0.25,
        }}
      >
        {title}
      </Text>
      <Text
        variant="bodySmall"
        style={{
          color: theme.colors.onSurfaceVariant,
          marginTop: 2,
        }}
      >
        {subtitle}
      </Text>
    </View>
  ) : (
    title
  );

  // Render right actions based on route
  const renderRightActions = () => {
    switch (route.name) {
      case 'home':
        return (
          <Appbar.Action
            icon="chart-bar"
            iconColor={theme.colors.onSurfaceVariant}
            onPress={() => router.push('/rankings')}
          />
        );
      case 'profile':
        return (
          <Appbar.Action
            icon="cog"
            iconColor={theme.colors.onSurfaceVariant}
            onPress={() => router.push('/(settings)')}
          />
        );
      default:
        return null;
    }
  };

  return (
    <Appbar.Header
      elevated
      style={{
        backgroundColor: theme.colors.surface,
      }}
    >
      {back && (
        <Appbar.BackAction
          onPress={navigation.goBack}
          iconColor={theme.colors.onSurfaceVariant}
        />
      )}

      <Appbar.Content
        title={titleContent}
        titleStyle={{
          fontSize: 22,
          fontWeight: '600',
          color: theme.colors.onSurface,
          letterSpacing: 0.25,
        }}
      />

      {renderRightActions()}
    </Appbar.Header>
  );
}
