import React from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import SettingsScreenContent from '@/features/settings/screens/SettingsScreen';
import { useAppTheme } from '@/hooks/useAppTheme';

// This route file now simply imports and renders the feature screen
// It also handles the top-level SafeAreaView for this route segment
export default function SettingsRoute() {
  const theme = useAppTheme();
  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: theme.colors.background }}>
      <SettingsScreenContent />
    </SafeAreaView>
  );
}