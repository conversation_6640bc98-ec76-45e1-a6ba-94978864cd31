import React from 'react';
import { Surface, Text } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'expo-router';

// Import custom hook
import { useProfileData } from '../hooks/useProfileData';

// Import components
import { ProfileScreenLoading } from '../components/ProfileScreenLoading';
import { ProfileScreenAuth } from '../components/ProfileScreenAuth';
import { ProfileScreenError } from '../components/ProfileScreenError';
import { ProfileScreenContent } from '../components/ProfileScreenContent';

export default function ProfileScreen() {
  const { t } = useTranslation();
  const theme = usePaperTheme();
  const router = useRouter();

  const {
    profile,
    profileError,
    userStories,
    storiesLoading,
    storiesError,
    isLoading,
    authUser,
    authStoreInitialized,
  } = useProfileData();

  const handleEditProfile = () => {
    router.push('/(profile)/edit');
  };

  const handleShareProfile = () => {
    console.log('Share Profile pressed');
  };

  const handleStoryPress = (storyId: string) => {
    console.log('Story pressed:', storyId);
    router.push(`/stories/${storyId}`);
  };

  const navigateToLogin = () => {
    router.push('/(auth)/login');
  };

  const navigateToRegister = () => {
    router.push('/(auth)/register');
  };

  // Show loading or uninitialized state if necessary
  if (!authStoreInitialized || isLoading) {
    return <ProfileScreenLoading />;
  }

  // If user is not authenticated after initialization
  if (!authUser) {
    return (
      <ProfileScreenAuth
        onLogin={navigateToLogin}
        onRegister={navigateToRegister}
      />
    );
  }

  // If authenticated but profile fetch failed
  if (!profile && profileError) {
    return <ProfileScreenError error={profileError} />;
  }

  // If authenticated but profile is somehow null and not loading (edge case)
  if (!profile) {
    return (
      <Surface
        style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: theme.colors.background,
        }}
      >
        <Text variant="bodyLarge">{t('loading')}</Text>
      </Surface>
    );
  }

  // User is authenticated and profile is loaded
  return (
    <ProfileScreenContent
      profile={profile}
      userStories={userStories}
      storiesLoading={storiesLoading}
      storiesError={storiesError}
      onEditProfile={handleEditProfile}
      onShareProfile={handleShareProfile}
      onStoryPress={handleStoryPress}
    />
  );
}
