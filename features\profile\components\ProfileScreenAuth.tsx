import React from 'react';
import { View } from 'react-native';
import { Surface, Text, Button } from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { usePaperTheme } from '@/hooks/usePaperTheme';

interface ProfileScreenAuthProps {
  onLogin: () => void;
  onRegister: () => void;
}

export function ProfileScreenAuth({
  onLogin,
  onRegister,
}: ProfileScreenAuthProps) {
  const { t } = useTranslation();
  const theme = usePaperTheme();

  return (
    <Surface
      style={{
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: theme.colors.background,
        padding: theme.spacing?.lg || 24,
      }}
    >
      <Text
        variant="bodyLarge"
        style={{
          color: theme.colors.onSurface,
          textAlign: 'center',
          marginBottom: theme.spacing?.xl || 32,
        }}
      >
        {t('profileAuthPrompt')}
      </Text>

      <View
        style={{
          width: '100%',
          maxWidth: 300,
          gap: theme.spacing?.md || 16,
        }}
      >
        <Button mode="contained" onPress={onLogin} icon="login">
          {t('loginButton')}
        </Button>

        <Button mode="outlined" onPress={onRegister} icon="account-plus">
          {t('registerButton')}
        </Button>
      </View>
    </Surface>
  );
}
