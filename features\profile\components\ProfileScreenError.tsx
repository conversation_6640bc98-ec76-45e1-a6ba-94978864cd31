import React from 'react';
import { Surface, Text, Icon } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';

interface ProfileScreenErrorProps {
  error: string;
}

export function ProfileScreenError({ error }: ProfileScreenErrorProps) {
  const theme = usePaperTheme();

  return (
    <Surface
      style={{
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: theme.colors.background,
        padding: theme.spacing?.lg || 24,
      }}
    >
      <Icon
        source="alert-circle"
        size={48}
        color={theme.colors.error}
        style={{ marginBottom: theme.spacing?.md || 16 }}
      />
      <Text
        variant="bodyLarge"
        style={{
          color: theme.colors.error,
          textAlign: 'center',
        }}
      >
        {error}
      </Text>
    </Surface>
  );
}
