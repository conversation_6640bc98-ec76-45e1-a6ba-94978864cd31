import React from 'react';
import { Text } from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { usePaperTheme } from '@/hooks/usePaperTheme';

export function ListHeader() {
  const theme = usePaperTheme();
  const { t } = useTranslation();

  return (
    <Text
      variant="titleMedium"
      style={{
        margin: theme.spacing?.md || 16,
        color: theme.colors.onSurface,
      }}
    >
      {t('social.feed.activityFeedTitle', '最新动态')}
    </Text>
  );
}
