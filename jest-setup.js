// Mock Platform.OS for expo-modules-core
jest.mock('expo-modules-core/src/Platform', () => ({
  OS: 'ios',
  // isTesting: true, // Add if necessary
  // select: (spec) => spec.ios || spec.native || spec.default, // Add if necessary
}));

// Mock react-native-paper's PaperProvider to provide a default icon
jest.mock('react-native-paper', () => {
  const React = require('react');
  const ReactNativePaper = jest.requireActual('react-native-paper');
  const { Text } = require('react-native');

  // A simple mock icon component
  const MockIconProvider = (props) => {
    // props will include name, size, color, direction
    return React.createElement(
      Text,
      { testID: props.testID || `mock-icon-${props.name}` },
      `Icon(${props.name})`
    );
  };

  return {
    ...ReactNativePaper,
    // Override PaperProvider to inject the mock icon setting
    PaperProvider: ({ children, theme, settings = {} }) => {
      const newSettings = {
        ...settings,
        icon: settings.icon || MockIconProvider, // Use user-provided icon setting if available, else default mock
      };
      return (
        <ReactNativePaper.PaperProvider theme={theme} settings={newSettings}>
          {children}
        </ReactNativePaper.PaperProvider>
      );
    },
  };
});

// Add a simple, direct mock for MaterialCommunityIcons as a fallback
jest.mock('@expo/vector-icons/MaterialCommunityIcons', () => {
  const React = require('react');
  const { Text } = require('react-native');
  // Return a simple functional component that renders Text
  // This avoids any class component lifecycle or setState issues.
  return (props) =>
    React.createElement(
      Text,
      { testID: props.testID || `mci-${props.name}` },
      `MCI(${props.name})`
    );
});

// Ensure other global setup (if any) remains
// For example, if you had: import '@testing-library/jest-native/extend-expect';
// (though you mentioned it was removed as RNTL includes them)
