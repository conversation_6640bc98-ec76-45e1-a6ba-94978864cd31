import React from 'react';
import { Avatar as PaperAvatar } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';

interface AvatarProps {
  uri?: string | null;
  username?: string | null;
  size?: number;
  testID?: string;
}

export function Avatar({ uri, username, size = 40, testID }: AvatarProps) {
  const theme = usePaperTheme();

  // Get initials from username
  const getInitials = (name?: string | null): string => {
    if (!name) return '?';

    const parts = name.trim().split(/\s+/);
    if (parts.length === 0) return '?';

    if (parts.length === 1) {
      return parts[0].charAt(0).toUpperCase();
    }

    return (
      parts[0].charAt(0) + parts[parts.length - 1].charAt(0)
    ).toUpperCase();
  };

  // If there's a valid URI, render the image avatar
  if (uri) {
    return (
      <PaperAvatar.Image
        size={size}
        source={{ uri }}
        testID={testID ? `${testID}-image` : 'avatar-image'}
      />
    );
  }

  // Otherwise, render the text avatar with initials
  return (
    <PaperAvatar.Text
      size={size}
      label={getInitials(username)}
      testID={testID ? `${testID}-text` : 'avatar-text'}
    />
  );
}
