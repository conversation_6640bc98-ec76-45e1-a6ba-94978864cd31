import React from 'react';
import { ScrollView, KeyboardAvoidingView, Platform, View } from 'react-native';
import { Surface } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { usePaperTheme } from '@/hooks/usePaperTheme';

// Custom hooks
import { useCreateStory } from '../hooks/useCreateStory';
import { useAISuggestions } from '../hooks/useAISuggestions';

// Components
import CreateStoryHeader from '../components/CreateStoryHeader';
import CreateStoryForm from '../components/CreateStoryForm';
import AISuggestionsSection from '../components/AISuggestionsSection';

export default function CreateStoryScreen() {
  const theme = usePaperTheme();

  // Story creation state and logic
  const {
    title,
    setTitle,
    initialContent,
    setInitialContent,
    submitting,
    contentFocused,
    setContentFocused,
    initialContentIsAI,
    setInitialContentIsAI,
    isFormValid,
    handleSubmit,
  } = useCreateStory();

  // AI suggestions state and logic
  const {
    aiSuggestions,
    loadingAISuggestions,
    showAISuggestions,
    handleFetchAISuggestions,
    handleSelectAISuggestion,
  } = useAISuggestions({
    getPrompt: () => `标题：${title}\n内容：${initialContent}`,
    onSelectSuggestion: (suggestion) => {
      setInitialContent((prevContent) => {
        const newContent = prevContent
          ? `${prevContent}\n${suggestion}`
          : suggestion;
        if (suggestion.trim().length > 0) {
          setInitialContentIsAI(true);
        }
        return newContent;
      });
    },
  });

  return (
    <SafeAreaView
      style={{ flex: 1, backgroundColor: theme.colors.background }}
      edges={['top']}
    >
      <Surface style={{ flex: 1 }} elevation={0}>
        <KeyboardAvoidingView
          style={{ flex: 1 }}
          behavior={Platform.OS === 'ios' ? 'padding' : undefined}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
        >
          <ScrollView
            style={{ flex: 1 }}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{
              flexGrow: 1,
              paddingHorizontal: 24, // MD3 推荐的边距
              paddingTop: 16,
              paddingBottom: 32,
            }}
          >
            {/* Header Section - MD3 规范的顶部间距 */}
            <View style={{ marginBottom: 32 }}>
              <CreateStoryHeader />
            </View>

            {/* Form Section - MD3 规范的表单布局 */}
            <View style={{ marginBottom: 24 }}>
              <CreateStoryForm
                title={title}
                onTitleChange={setTitle}
                initialContent={initialContent}
                onContentChange={setInitialContent}
                contentFocused={contentFocused}
                onContentFocus={() => setContentFocused(true)}
                onContentBlur={() => setContentFocused(false)}
                isFormValid={isFormValid}
                submitting={submitting}
                onSubmit={handleSubmit}
              />
            </View>

            {/* AI Suggestions Section - MD3 规范的底部间距 */}
            <View style={{ flex: 1, minHeight: 200 }}>
              <AISuggestionsSection
                onFetchSuggestions={handleFetchAISuggestions}
                loadingSuggestions={loadingAISuggestions}
                showSuggestions={showAISuggestions}
                suggestions={aiSuggestions}
                onSelectSuggestion={handleSelectAISuggestion}
                disabled={submitting}
              />
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
      </Surface>
    </SafeAreaView>
  );
}
