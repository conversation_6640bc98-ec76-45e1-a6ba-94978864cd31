import React from 'react';
import { View, FlatList } from 'react-native';
import { Text, ActivityIndicator } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import StoryPreviewCard from '@/components/stories/StoryPreviewCard';
import { Story } from '@/types/story';
import { useTranslation } from 'react-i18next';

interface MyStoriesSectionProps {
  stories: Story[];
  onStoryPress?: (storyId: string) => void;
  loading?: boolean;
  error?: string | null;
}

export function MyStoriesSection({
  stories,
  onStoryPress,
  loading,
  error,
}: MyStoriesSectionProps) {
  const theme = usePaperTheme();
  const { t } = useTranslation();

  // 渲染单个故事预览项
  const renderStoryItem = ({ item }: { item: Story }) => (
    <StoryPreviewCard story={item} onPress={() => onStoryPress?.(item.id)} />
  );

  return (
    <View
      style={{
        marginBottom: theme.spacing?.lg || 24,
      }}
    >
      {/* Section Title with MD3 typography */}
      <View
        style={{
          paddingHorizontal: theme.spacing?.lg || 24,
          marginBottom: theme.spacing?.lg || 24,
        }}
      >
        <Text
          variant="headlineSmall"
          style={{
            color: theme.colors.onBackground,
            fontWeight: '600',
            letterSpacing: 0.25,
          }}
        >
          {t('profile.myStories', 'My Stories')}
        </Text>
      </View>

      {/* Loading State */}
      {loading && (
        <View
          style={{
            alignItems: 'center',
            paddingVertical: theme.spacing?.xl || 32,
          }}
        >
          <ActivityIndicator size="large" />
        </View>
      )}

      {/* Error State */}
      {!loading && error && (
        <Text
          variant="bodyMedium"
          style={{
            color: theme.colors.error,
            textAlign: 'center',
            paddingHorizontal: theme.spacing?.md || 16,
            paddingVertical: theme.spacing?.lg || 24,
          }}
        >
          {error}
        </Text>
      )}

      {/* Stories List */}
      {!loading && !error && stories && stories.length > 0 ? (
        <FlatList
          horizontal
          showsHorizontalScrollIndicator={false}
          data={stories}
          renderItem={renderStoryItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={{
            paddingHorizontal: theme.spacing?.lg || 24,
            paddingVertical: theme.spacing?.md || 16,
          }}
        />
      ) : (
        !loading &&
        !error && (
          <Text
            variant="bodyMedium"
            style={{
              color: theme.colors.onSurfaceVariant,
              textAlign: 'center',
              paddingHorizontal: theme.spacing?.md || 16,
              paddingVertical: theme.spacing?.lg || 24,
            }}
          >
            {t('profile.noStoriesYet', "You haven't created any stories yet.")}
          </Text>
        )
      )}
    </View>
  );
}
