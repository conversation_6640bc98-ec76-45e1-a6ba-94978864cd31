import React, { useEffect } from 'react';
import { Badge } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import { getUnreadMessageCount } from '@/api/messages';

interface MessageBadgeProps {
  count?: number;
  size?: 'small' | 'medium' | 'large';
  autoRefresh?: boolean;
  refreshInterval?: number;
  onCountChange?: (count: number) => void;
}

export default function MessageBadge({
  count: propCount,
  size = 'medium',
  autoRefresh = false,
  refreshInterval = 60000, // 1分钟
  onCountChange,
}: MessageBadgeProps) {
  const theme = usePaperTheme();
  const [count, setCount] = React.useState(propCount || 0);

  // 如果外部传入了 count，使用外部的 count
  useEffect(() => {
    if (propCount !== undefined) {
      setCount(propCount);
    }
  }, [propCount]);

  // 如果没有外部传入 count，自动获取未读消息数量
  useEffect(() => {
    if (propCount === undefined) {
      fetchUnreadCount();
    }
  }, [propCount]);

  // 自动刷新
  useEffect(() => {
    if (autoRefresh && propCount === undefined) {
      const intervalId = setInterval(() => {
        fetchUnreadCount();
      }, refreshInterval);

      return () => clearInterval(intervalId);
    }
  }, [autoRefresh, refreshInterval, propCount]);

  // 获取未读消息数量
  const fetchUnreadCount = async () => {
    try {
      const { count: unreadCount, error } = await getUnreadMessageCount();
      if (error) {
        throw new Error(error.message);
      }
      setCount(unreadCount);
      onCountChange?.(unreadCount);
    } catch (err) {
      console.error('Error fetching unread count:', err);
    }
  };

  // 如果没有未读消息，不显示徽章
  if (count === 0) {
    return null;
  }

  // 根据 size 获取 Badge size
  const getBadgeSize = () => {
    switch (size) {
      case 'small':
        return 16;
      case 'large':
        return 24;
      case 'medium':
      default:
        return 20;
    }
  };

  // 格式化数量
  const formatCount = (count: number) => {
    if (count > 99) {
      return '99+';
    }
    return count.toString();
  };

  if (count <= 0) return null;

  return (
    <Badge
      visible={count > 0}
      size={getBadgeSize()}
      style={{
        position: 'absolute',
        top: -5,
        right: -5,
        backgroundColor: theme.colors.error,
        zIndex: 1,
      }}
    >
      {formatCount(count)}
    </Badge>
  );
}
