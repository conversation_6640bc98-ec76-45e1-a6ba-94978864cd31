import React, { useEffect, useState } from 'react';
import { View } from 'react-native';
import { SegmentedButtons } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import { useTranslation } from 'react-i18next';
import NotificationBadge from '@/features/notifications/components/NotificationBadge';
import MessageBadge from '@/features/messages/components/MessageBadge';
import { getUnreadNotificationCount } from '@/api/notifications';
import { getUnreadMessageCount } from '@/api/messages';

export type SocialTabKey = 'feed' | 'discover' | 'messages' | 'notifications';

interface SocialTabsWithBadgesProps {
  activeTab: SocialTabKey;
  onTabPress: (tabKey: SocialTabKey) => void;
}

const TABS: SocialTabKey[] = ['feed', 'discover', 'messages', 'notifications'];

// 图标映射到 Material Community Icons
const getTabIcon = (tabKey: SocialTabKey): string => {
  switch (tabKey) {
    case 'feed':
      return 'trending-up';
    case 'discover':
      return 'magnify';
    case 'messages':
      return 'message-outline';
    case 'notifications':
      return 'bell-outline';
    default:
      return 'help';
  }
};

export function SocialTabsWithBadges({ activeTab, onTabPress }: SocialTabsWithBadgesProps) {
  const theme = usePaperTheme();
  const { t } = useTranslation();
  const [unreadNotificationCount, setUnreadNotificationCount] = useState(0);
  const [unreadMessageCount, setUnreadMessageCount] = useState(0);

  // 获取未读通知数量
  useEffect(() => {
    const fetchUnreadNotificationCount = async () => {
      try {
        const { count } = await getUnreadNotificationCount();
        setUnreadNotificationCount(count);
      } catch (error) {
        console.error('Error fetching unread notification count:', error);
      }
    };

    fetchUnreadNotificationCount();

    // 设置定时器，每分钟刷新一次
    const intervalId = setInterval(fetchUnreadNotificationCount, 60000);

    return () => clearInterval(intervalId);
  }, []);

  // 获取未读消息数量
  useEffect(() => {
    const fetchUnreadMessageCount = async () => {
      try {
        const { count } = await getUnreadMessageCount();
        setUnreadMessageCount(count);
      } catch (error) {
        console.error('Error fetching unread message count:', error);
      }
    };

    fetchUnreadMessageCount();

    // 设置定时器，每分钟刷新一次
    const intervalId = setInterval(fetchUnreadMessageCount, 60000);

    return () => clearInterval(intervalId);
  }, []);

  // 获取标签文本
  const getTabLabel = (tabKey: SocialTabKey): string => {
    switch (tabKey) {
      case 'feed':
        return t('social.tabs.feed', '动态');
      case 'discover':
        return t('social.tabs.discover', '发现');
      case 'messages':
        return t('social.tabs.messages', '消息');
      case 'notifications':
        return t('social.tabs.notifications', '通知');
      default:
        return '';
    }
  };

  // 构建 SegmentedButtons 的 buttons 数组
  const buttons = TABS.map((tabKey) => ({
    value: tabKey,
    label: getTabLabel(tabKey),
    icon: getTabIcon(tabKey),
  }));

  return (
    <View style={{ position: 'relative' }}>
      <SegmentedButtons
        value={activeTab}
        onValueChange={(value) => onTabPress(value as SocialTabKey)}
        buttons={buttons}
        style={{
          marginHorizontal: theme.spacing?.md || 16,
          marginVertical: theme.spacing?.sm || 8,
        }}
      />
      
      {/* 消息角标 - 绝对定位在消息按钮上 */}
      {unreadMessageCount > 0 && (
        <View
          style={{
            position: 'absolute',
            top: 8,
            right: '37.5%', // 消息按钮大概位置（4个按钮，第3个）
            zIndex: 10,
          }}
        >
          <MessageBadge count={unreadMessageCount} size="small" />
        </View>
      )}
      
      {/* 通知角标 - 绝对定位在通知按钮上 */}
      {unreadNotificationCount > 0 && (
        <View
          style={{
            position: 'absolute',
            top: 8,
            right: '12.5%', // 通知按钮大概位置（4个按钮，第4个）
            zIndex: 10,
          }}
        >
          <NotificationBadge count={unreadNotificationCount} size="small" />
        </View>
      )}
    </View>
  );
}
